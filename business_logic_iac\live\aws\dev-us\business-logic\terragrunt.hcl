dependency "network" {
  config_path = "../network"
}

dependency "dns" {
  config_path = "../dns"
}

dependency "sns_slack_notifications" {
  config_path = "../sns-slack-notifications"

  mock_outputs_allowed_terraform_commands = ["validate", "show"]
  mock_outputs = {
    sns_topic_arn  = "arn:aws:sns:us-east-1:123456789012:mock-topic"
    sns_topic_name = "mock-topic"
    sns_topic_id   = "mock-topic"
  }

  # Force dependency to be read from state, not cache
  skip_outputs = false
}

inputs = {
  country               = "us"
  ec2_key_pair_name     = "dev-us-comtech-analytics"
  environment           = "dev"
  mariadb_instance_size = "db.t4g.large"
  mariadb_max_allocated_storage = 65536
  #  TODO: Isolate Azure datacenters IP ranges which will access this MadiaDB replica node
  mariadb_replica_cidr_blocks = ["0.0.0.0/0"]
  private_subnets_ids = dependency.network.outputs.private_subnets_ids
  vpc_cidr_block      = dependency.network.outputs.vpc_cidr_block
  aws_region = "us-east-1"
  tags = merge(
    include.root.locals.tags,
    {
      ChargeCode  = "03SMAL.GENN.0000.WA9.DEV"
      country     = "us"
      environment = "dev"
    }
  )
  # CollectorAPI AMI us-east-1
  collectorapi_ami    = "ami-054b7f3ae6c67720b"
  collectorapi_instance_size = "t3a.medium"
  vpc_id              = dependency.network.outputs.vpc_id
  api_acm_arn         = dependency.dns.outputs.api_acm_arn
  api_subdomain_name  = dependency.dns.outputs.api_subdomain_name
  domain_hosted_zone_id = dependency.dns.outputs.domain_hosted_zone_id

  # SNS topic for CloudWatch alarms
  alarm_sns_topic_arn = dependency.sns_slack_notifications.outputs.sns_topic_arn
}

terraform {
  source = "../../../../modules/business-logic/"
  #source = "git::ssh://****************************/cloud-infrastructure/comtechanalytics-infrastructure.git//business_logic_iac/modules/network?ref=main"
}

include "root" {
  expose = true
  path   = find_in_parent_folders()
}

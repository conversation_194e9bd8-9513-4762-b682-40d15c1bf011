# Comtech SmartAnalytics: Backend terraform code

This repo contains Terrform modules to creates the resources required to deploy the Comtech smartAnalytics V1 backend

The following image show a snippet of the backend resources and their connections

![Resources snippet](images/resources.png)

# New environment set up

Prerequisites
    - country
    - environment

Create a new folder under business_logic_iac/live/aws/<environment>-<country>.  
In this folder, create a files and folders as shown in the following example:

```
comtechanalytics-infrastructure/
├─ business_logic_iac/
│  ├─ images/
│  ├─ live/
│  ├─ modules
│  │  ├─ aws/
│  │  │  ├─ dev-us/
│  │  │  │  ├─business-logic/
│  │  │  │  │  ├─.terraform.lock.hcl
│  │  │  │  │  ├─terragrunt.hcl
│  │  │  │  ├─client-vpn/
│  │  │  │  │  ├─.terraform.lock.hcl
│  │  │  │  │  ├─dev-us-metadata.xml
│  │  │  │  │  ├─terragrunt.hcl
│  │  │  │  ├─dns/
│  │  │  │  │  ├─.terraform.lock.hcl
│  │  │  │  │  ├─terragrunt.hcl
│  │  │  │  ├─network/
│  │  │  │  │  ├─.terraform.lock.hcl
│  │  │  │  │  ├─terragrunt.hcl
│  │  │  │  ├─power-bi-gateway/
│  │  │  │  │  ├─.terraform.lock.hcl
│  │  │  │  │  ├─terragrunt.hcl
│  │  │  │  ├─power-bi-desktop/
│  │  │  │  │  ├─.terraform.lock.hcl
│  │  │  │  │  ├─terragrunt.hcl
|  │  │  │  ├─bucket/
│  │  │  │  │  ├─.terraform.lock.hcl
│  │  │  │  │  ├─terragrunt.hcl
│  │  │  │  ├─sns-slack-notifications/
│  │  │  │  │  ├─.terraform.lock.hcl
│  │  │  │  │  ├─terragrunt.hcl
│  │  │  │  ├─kms/
│  │  │  │  │  ├─.terraform.lock.hcl
│  │  │  │  │  ├─terragrunt.hcl
│  │  ├─ .tflint.hcl
│  │  ├─ terragrunt.hcl
│  ├─ README.md
├─ .gitignore
├─ .gitlab-ci.yml
├─ README.md
```

NOTE: To create the .terraform.lock.hcl in each of the folders, please use the following command:

```
AWS_PROFILE=dev-nsoc terragrunt init -backend=false
```

## network

The following is an example of the terragrunt.hcl file

```
inputs = {
  availability_zones = [
    "us-east-1b",
    "us-east-1c",
  ]
  country     = "us"
  environment = "dev"
  private_subnet_cidrs = [
    "10.0.5.0/24",
    "10.0.6.0/24",
  ]
  public_subnet_cidrs = [
    "10.0.1.0/24",
    "********/24",
  ]
  tags = merge(
    include.root.locals.tags,
    {
      ChargeCode  = "03SMAL.GENN.0000.WA9.DEV"
      country     = "us"
      environment = "dev"
    }
  )
  vpc_cidr_block = "10.0.0.0/16"
}

terraform {
  source = "../../../../modules/network/"
  #source = "git::ssh://****************************/cloud-infrastructure/comtechanalytics-infrastructure.git//business_logic_iac/modules/network?ref=main"
}

include "root" {
  expose = true
  path   = find_in_parent_folders()
}
```

A description for the inputs to the network modules can be found [here](https://gitlab.nsoc.state911.net/cloud-infrastructure/comtechanalytics-infrastructure/-/tree/main/business_logic_iac/modules/network)


## dns

Prerequisites

- Have an existing public hosted zone with a domain name [here](https://us-east-1.console.aws.amazon.com/route53/v2/hostedzones?region=us-east-1#)

The following is an example of the terragrunt.hcl file

```
inputs = {
  country               = "us"
  environment           = "dev"
  aws_region            = "us-east-1"
  tags = merge(
    include.root.locals.tags,
    {
      ChargeCode  = "03SMAL.GENN.0000.WA9.DEV"
      country     = "us"
      environment = "dev"
    }
  )
  root_domain_name      = "dev-nsoc.state911.net"
}

terraform {
  source = "../../../../modules/dns/"
}

include "root" {
  expose = true
  path   = find_in_parent_folders()
}
```

A description for the inputs to the DNS modules can be found [here](https://gitlab.nsoc.state911.net/cloud-infrastructure/comtechanalytics-infrastructure/-/tree/main/business_logic_iac/modules/dns)


## kms

Prerequisites

- None (KMS module has no dependencies)

The KMS module creates customer-specific encryption keys for data isolation and security. Each customer gets their own dedicated KMS key.

The following is an example of the terragrunt.hcl file

```
inputs = {
  aws_region = "us-east-1"
  environment = "dev"
  country = "us"
  customer_config = {
    "washingtoncounty-mn" = {
      name = "washingtoncounty-mn"
      enabled = true
    }
    "flagler" = {
      name = "flagler"
      enabled = true
    }
    "pvgt" = {
      name = "pvgt"
      enabled = true
    }
  }

  tags = merge(
    include.root.locals.tags,
    {
      ChargeCode  = "03SMAL.GENN.0000.WA9.DEV"
      country     = "us"
      environment = "dev"
      Module      = "kms"
    }
  )
}

terraform {
  source = "../../../../modules/kms/"
}

include "root" {
  expose = true
  path   = find_in_parent_folders()
}
```

A description for the inputs to the KMS modules can be found [here](https://gitlab.nsoc.state911.net/cloud-infrastructure/comtechanalytics-infrastructure/-/tree/main/business_logic_iac/modules/kms)


## buckets

Prerequisites

- KMS module must be deployed first (for encryption keys)

The S3 bucket module creates customer-specific S3 buckets for various use cases including Lambda deployment packages and data storage.

The following is an example of the terragrunt.hcl file

```
dependency "kms" {
  config_path = "../kms"
}

inputs = {
  aws_region = "us-east-1"
  environment = "dev"
  country = "us"
  bucket_name = "lambdas"

  customer_config = {
    for customer in ["washingtoncounty-mn", "flagler", "pvgt"] : customer => {
      enabled = true
      kms_key_arn = dependency.kms.outputs.kms_key_arns[customer]
      customer_name = customer
    }
  }

  s3_lifecycle_noncurrent_version_expiration_days = 30

  tags = merge(
    include.root.locals.tags,
    {
      ChargeCode  = "03SMAL.GENN.0000.WA9.DEV"
      country     = "us"
      environment = "dev"
      Module      = "s3"
    }
  )
}

terraform {
  source = "../../../../modules/bucket/"
}

include "root" {
  expose = true
  path   = find_in_parent_folders()
}
```

A description for the inputs to the S3 bucket modules can be found [here](https://gitlab.nsoc.state911.net/cloud-infrastructure/comtechanalytics-infrastructure/-/tree/main/business_logic_iac/modules/bucket)


## business-logic

Prerequisites

- Create key pair
- Create MariaDB parameter store secrets
- Create CollectorAPI AMI for environment
- Deploy sns-slack-notifications module first (for alarm integration)

NOTE:  The credentails to MariaDB (username and password) should stored in secrets manager with thr following path format:

```
/comtech-analytics/<environment>/<country>/mariadb_credentials
```

**Integrated CloudWatch Alarms:**
The business-logic module now includes comprehensive CloudWatch alarms for:
- EC2 instances (CPU, status checks)
- RDS master and replica (CPU, memory, storage, replication lag)
- ALB (unhealthy hosts, response time, 4XX/5XX errors)

Alarms automatically send notifications to Slack via the shared SNS topic.


The following is an example of the terragrunt.hcl file

```
dependency "network" {
  config_path = "../network"
}

dependency "dns" {
  config_path = "../dns"
}

inputs = {
  country               = "us"
  ec2_key_pair_name     = "dev-us-comtech-analytics"
  environment           = "dev"
  mariadb_instance_size = "db.t3.medium"
  mariadb_max_allocated_storage = 16384
  #  TODO: Isolate Azure datacenters IP ranges which will access this MadiaDB replica node
  mariadb_replica_cidr_blocks = ["0.0.0.0/0"]
  private_subnets_ids = dependency.network.outputs.private_subnets_ids
  vpc_cidr_block      = dependency.network.outputs.vpc_cidr_block
  region = "us-east-1"
  tags = merge(
    include.root.locals.tags,
    {
      ChargeCode  = "03SMAL.GENN.0000.WA9.DEV"
      country     = "us"
      environment = "dev"
    }
  )
  # CollectorAPI AMI us-east-1
  collectorapi_ami    = "ami-0d7f7cd504176c778"
  vpc_id              = dependency.network.outputs.vpc_id
  api_acm_arn         = dependency.dns.outputs.api_acm_arn
  api_subdomain_name  = dependency.dns.outputs.api_subdomain_name
  domain_hosted_zone_id = dependency.dns.outputs.domain_hosted_zone_id
}

terraform {
  source = "../../../../modules/business-logic/"
  #source = "git::ssh://****************************/cloud-infrastructure/comtechanalytics-infrastructure.git//business_logic_iac/modules/network?ref=main"
}

include "root" {
  expose = true
  path   = find_in_parent_folders()
}

```

Refer to the [documentation](https://docs.aws.amazon.com/AmazonRDS/latest/UserGuide/CHAP_Storage.html#CHAP_Storage.Other.Factors) to determine the appropriate value for mariadb_max_allocated_storage depending on mariadb_instance_size.

A description for the inputs to the network modules can be found [here](https://gitlab.nsoc.state911.net/cloud-infrastructure/comtechanalytics-infrastructure/-/tree/main/business_logic_iac/modules/business-logic)

## power-bi-gateway

Prerequisites

- Power BI Gatway AMI 

NOTE:  If you can not copy the AMI from an existing region or cross accounts, use the following [instructions](https://confluence.comtech-idn.com/display/SMAN/Power+Bi+On-premises+Data+Gateway+Setup) to set up Power BI Gateway on a Windows server ec2 instance and then create an AMI.


The following is an example of the terragrunt.hcl file

```
dependency "network" {
  config_path = "../network"
}

inputs = {
  aws_power_bi_data_gateway_ami = "ami-06dede1f89773d9ed"
  aws_private_subnet            = dependency.network.outputs.private_subnets_ids
  aws_region                    = "us-east-1"
  aws_vpc_id                    = dependency.network.outputs.vpc_id
  vpc_cidr_block                = dependency.network.outputs.vpc_cidr_block
  country                       = "us"
  environment                   = "dev"
  key_pair_name                 = "dev-us-comtech-smartanalytics-key-pair"
  tags = merge(
    include.root.locals.tags,
    {
      ChargeCode  = "03SMAL.GENN.0000.WA9.DEV"
      country     = "us"
      environment = "dev"
    }
  )
}

terraform {
  source = "../../../../modules/power-bi-gateway/"
  #source = "git::ssh://****************************/cloud-infrastructure/comtechanalytics-infrastructure.git//business_logic_iac/modules/network?ref=main"
}

include "root" {
  expose = true
  path   = find_in_parent_folders()
}

```

A description for the inputs to the power-bi-gateway modules can be found [here](https://gitlab.nsoc.state911.net/cloud-infrastructure/comtechanalytics-infrastructure/-/tree/main/business_logic_iac/modules/power-bi-gateway)

## sns-slack-notifications

Prerequisites

- Slack webhook URL stored in HashiCorp Vault
- Proper Vault authentication and permissions

The SNS Slack notifications module creates a shared SNS topic with Slack integration that can be used by multiple modules for CloudWatch alarm notifications.

The following is an example of the terragrunt.hcl file

```
inputs = {
  aws_region  = "us-east-1"
  environment = "dev"
  country     = "us"

  # Vault path for Slack webhook URL
  slack_webhook_url_vault_path = "secret/dev-nsoc/slack_webhooks/smartanalytics-alerts-dev"

  tags = merge(
    include.root.locals.tags,
    {
      ChargeCode  = "03SMAL.GENN.0000.WA9.DEV"
      country     = "us"
      environment = "dev"
      Module      = "sns-slack-notifications"
    }
  )
}

terraform {
  source = "../../../../modules/sns-slack-notifications/"
}

include "root" {
  expose = true
  path   = find_in_parent_folders()
}
```

A description for the inputs to the sns-slack-notifications modules can be found [here](https://gitlab.nsoc.state911.net/cloud-infrastructure/comtechanalytics-infrastructure/-/tree/main/business_logic_iac/modules/sns-slack-notifications)

## power-bi-desktop

Prerequisites

- Power BI desktop AMI 


The following is an example of the terragrunt.hcl file

```
dependency "network" {
  config_path = "../network"
}

inputs = {
  aws_power_bi_desktop_ami      = "ami-06dede1f89773d9ed"
  aws_private_subnet            = dependency.network.outputs.private_subnets_ids
  aws_region                    = "us-east-1"
  aws_vpc_id                    = dependency.network.outputs.vpc_id
  country                       = "us"
  environment                   = "dev"
  key_pair_name                 = "dev-us-comtech-smartanalytics-key-pair"
  tags = merge(
    include.root.locals.tags,
    {
      ChargeCode  = "03SMAL.GENN.0000.WA9.DEV"
      country     = "us"
      environment = "dev"
    }
  )
}

terraform {
  source = "../../../../modules/power-bi-desktop/"
  #source = "git::ssh://****************************/cloud-infrastructure/comtechanalytics-infrastructure.git//business_logic_iac/modules/network?ref=main"
}

include "root" {
  expose = true
  path   = find_in_parent_folders()
}
```

A description for the inputs to the network modules can be found [here](https://gitlab.nsoc.state911.net/cloud-infrastructure/comtechanalytics-infrastructure/-/tree/main/business_logic_iac/modules/power-bi-desktop)

## client-vpn

Prerequisites

- SAML Okta VPN integration, steps can be found [here](https://solacomtech.atlassian.net/wiki/spaces/IN/pages/2786689046/Client+VPN+Setup)

The following is an example of the terragrunt.hcl file

```
dependency "network" {
  config_path = "../network"
}

inputs = {
  authorization_rules       = {
    local = {
      access_group_id = null
      cidr            = "0.0.0.0/0"
      description     = "Allow inbound route for local VPC"
    }
  }
  banner_text               = "Welcome to Comtech smartAnalytics!"
  certificate_domain_zone   = "dev-nsoc.state911.net"
  client_cidr_block         = "**********/22"
  cloudwatch_log_group_name = "dev-us-comtech-smartanalytics-client-vpn-cloudwatch"
  country                   = "us"  
  description               = "dev-us-comtech-smartanalytics-client-vpn"
  dns_servers               = [
    cidrhost(dependency.network.outputs.vpc_cidr_block, 2)
  ]
  environment               = "dev"  
  saml_iam_provider_name    = "dev-us-comtech-smartanalytics-client-vpn-okta"
  saml_metadata_document    = file("dev-us-metadata.xml")
  subnet_ids                = dependency.network.outputs.private_subnets_ids
  vpc_id                    = dependency.network.outputs.vpc_id

  tags = merge(
    include.root.locals.tags,
    {
      ChargeCode  = "03SMAL.GENN.0000.WA9.DEV"
      country     = "us"
      environment = "dev"
    }
  )
}

terraform {
  source = "../../../../modules/client-vpn/"
}

include "root" {
  expose = true
  path   = find_in_parent_folders()
}
```

A description for the inputs to the client VPN modules can be found [here](https://gitlab.nsoc.state911.net/cloud-infrastructure/comtechanalytics-infrastructure/-/tree/main/business_logic_iac/modules/client-vpn)

NOTES:
- [client_vpn_saml module](https://gitlab.nsoc.state911.net/cloud-infrastructure/aws-modules#client_vpn_saml)
- Please reach out to SOC for the OKTA portion where they will give you the data needed to create the file: dev-us-metadata.xml
- Due to a limitation in client_vpn_saml, run the following command in the folder:

```
AWS_DEFAULT_REGION=us-east-1 AWS_PROFILE=dev-nsoc terragrunt apply -target 'module.client_vpn.aws_acm_certificate.vpn'
```

## Module deployment order

The modules must be deployed in the following order due to dependencies:

### **Phase 1: Foundation (No Dependencies)**
1. **network** - VPC and networking infrastructure
2. **dns** - Route53 hosted zones and ACM certificates
3. **kms** - Customer-specific encryption keys
4. **sns-slack-notifications** - Shared SNS topic for CloudWatch alarms

### **Phase 2: Storage (Depends on KMS)**
5. **bucket** - Customer-specific S3 buckets with KMS encryption

### **Phase 3: Applications (Depends on Foundation)**
6. **business-logic** - Core application with integrated monitoring
   - **Dependencies**: network, dns, sns-slack-notifications
   - **Features**: MariaDB, CollectorAPI, Lambda functions, CloudWatch alarms

### **Phase 4: Access & Analytics (Depends on Network)**
7. **client-vpn** - VPN access for secure connectivity
8. **power-bi-gateway** - Data gateway for Power BI integration
9. **power-bi-desktop** - Desktop instances for data analysis


## Application configuration

- MariaDB
- Business logic application
- Power BI gateway

## GitLab Pipeline

Using the GitLab pipeline editor, similar code should be added to .plan-template and .deploy-template sections:

```
plan-dev-us-network:
  extends: .plan-template
  variables:
    REGION: us-east-1
    ENVIRONMENT: dev-us
    MODULE: network
    ACCOUNT: dev-nsoc
    PARTITION: aws
  rules:
    - if: $CI_PROJECT_NAMESPACE != "cloud-infrastructure"
      when: never
    - if: $CI_PROJECT_NAMESPACE == "cloud-infrastructure" && $CI_PIPELINE_SOURCE == "merge_request_event"
    - if: $CI_COMMIT_BRANCH == "main"

plan-dev-us-power-bi-gateway:
  extends: .plan-template
  variables:
    REGION: us-east-1
    ENVIRONMENT: dev-us
    MODULE: power-bi-gateway
    ACCOUNT: dev-nsoc
    PARTITION: aws
  rules:
    - if: $CI_PROJECT_NAMESPACE != "cloud-infrastructure"
      when: never
    - if: $CI_PROJECT_NAMESPACE == "cloud-infrastructure" && $CI_PIPELINE_SOURCE == "merge_request_event"
    - if: $CI_COMMIT_BRANCH == "main"

plan-dev-us-business-logic:
  extends: .plan-template
  variables:
    REGION: us-east-1
    ENVIRONMENT: dev-us
    MODULE: business-logic
    ACCOUNT: dev-nsoc
    PARTITION: aws
  rules:
    - if: $CI_PROJECT_NAMESPACE != "cloud-infrastructure"
      when: never
    - if: $CI_PROJECT_NAMESPACE == "cloud-infrastructure" && $CI_PIPELINE_SOURCE == "merge_request_event"
    - if: $CI_COMMIT_BRANCH == "main"

plan-dev-us-client-vpn:
  extends: .plan-template
  variables:
    REGION: us-east-1
    ENVIRONMENT: dev-us
    MODULE: client-vpn
    ACCOUNT: dev-nsoc
    PARTITION: aws
  rules:
    - if: $CI_PROJECT_NAMESPACE != "cloud-infrastructure"
      when: never
    - if: $CI_PROJECT_NAMESPACE == "cloud-infrastructure" && $CI_PIPELINE_SOURCE == "merge_request_event"
    - if: $CI_COMMIT_BRANCH == "main"

plan-dev-us-dns:
  extends: .plan-template
  variables:
    REGION: us-east-1
    ENVIRONMENT: dev-us
    MODULE: dns
    ACCOUNT: dev-nsoc
    PARTITION: aws
  rules:
    - if: $CI_PROJECT_NAMESPACE != "cloud-infrastructure"
      when: never
    - if: $CI_PROJECT_NAMESPACE == "cloud-infrastructure" && $CI_PIPELINE_SOURCE == "merge_request_event"
    - if: $CI_COMMIT_BRANCH == "main"
```

```
deploy-dev-us-network:
  extends: .deploy-template
  variables:
    REGION: us-east-1
    ENVIRONMENT: dev-us
    MODULE: network
    ACCOUNT: dev-nsoc
    PARTITION: aws
  environment:
    name: ${ENVIRONMENT}-${MODULE}
    deployment_tier: development
  rules:
    - if: $CI_PROJECT_NAMESPACE != "cloud-infrastructure"
      when: never
    - if: $CI_COMMIT_BRANCH == "main"
      when: manual
  needs:
    - plan-dev-us-network

deploy-dev-us-power-bi-gateway:
  extends: .deploy-template
  variables:
    REGION: us-east-1
    ENVIRONMENT: dev-us
    MODULE: power-bi-gateway
    ACCOUNT: dev-nsoc
    PARTITION: aws
  environment:
    name: ${ENVIRONMENT}-${MODULE}
    deployment_tier: development
  rules:
    - if: $CI_PROJECT_NAMESPACE != "cloud-infrastructure"
      when: never
    - if: $CI_COMMIT_BRANCH == "main"
      when: manual
  needs:
    - plan-dev-us-power-bi-gateway

deploy-dev-us-business-logic:
  extends: .deploy-template
  variables:
    REGION: us-east-1
    ENVIRONMENT: dev-us
    MODULE: business-logic
    ACCOUNT: dev-nsoc
    PARTITION: aws
  environment:
    name: ${ENVIRONMENT}-${MODULE}
    deployment_tier: development
  rules:
    - if: $CI_PROJECT_NAMESPACE != "cloud-infrastructure"
      when: never
    - if: $CI_COMMIT_BRANCH == "main"
      when: manual
  needs:
    - plan-dev-us-business-logic

deploy-dev-us-client-vpn:
  extends: .deploy-template
  variables:
    REGION: us-east-1
    ENVIRONMENT: dev-us
    MODULE: client-vpn
    ACCOUNT: dev-nsoc
    PARTITION: aws
  environment:
    name: ${ENVIRONMENT}-${MODULE}
    deployment_tier: development
  rules:
    - if: $CI_PROJECT_NAMESPACE != "cloud-infrastructure"
      when: never
    - if: $CI_COMMIT_BRANCH == "main"
      when: manual
  needs:
    - plan-dev-us-client-vpn

deploy-dev-us-dns:
  extends: .deploy-template
  variables:
    REGION: us-east-1
    ENVIRONMENT: dev-us
    MODULE: dns
    ACCOUNT: dev-nsoc
    PARTITION: aws
  environment:
    name: ${ENVIRONMENT}-${MODULE}
    deployment_tier: development
  rules:
    - if: $CI_PROJECT_NAMESPACE != "cloud-infrastructure"
      when: never
    - if: $CI_COMMIT_BRANCH == "main"
      when: manual
  needs:
    - plan-dev-us-dns
```

<!--- BEGIN_TF_DOCS --->
## Requirements

| Name | Version |
|------|---------|
| <a name="requirement_terraform"></a> [terraform](#requirement\_terraform) | ~> 1.6 |
| <a name="requirement_aws"></a> [aws](#requirement\_aws) | ~> 5.24 |

## Providers

| Name | Version |
|------|---------|
| <a name="provider_aws"></a> [aws](#provider\_aws) | ~> 5.24 |

## Modules

| Name | Source | Version |
|------|--------|---------|
| <a name="module_kms"></a> [aws](#module\_kms) | [KMS Docs](modules/kms/README.md) | n/a |
| <a name="module_bucket"></a> [aws](#module\_bucket) | [Bucket Docs](modules/bucket/README.md) | n/a |
| <a name="module_client_vpn"></a> [aws](#module\_client-vpn) | [Client VPN Docs](modules/client-vpn/README.md) | n/a |
| <a name="module_dns"></a> [aws](#module\_dns) | [DNS Docs](modules/dns/README.md) | n/a |
| <a name="module_business_logic"></a> [aws](#module\_business-logic) | [Business Logic Docs](modules/business-logic/README.md) | n/a |
| <a name="module_network"></a> [aws](#module\_network) | [Network Docs](modules/network/README.md) | n/a |
| <a name="module_power-bi-gateway"></a> [aws](#module\_power-bi-gateway) | [Power BI Gateway Docs](modules/power-bi-gateway/README.md) | n/a |
| <a name="module_power-bi-desktop"></a> [aws](#module\_power-bi-desktop) | [Power BI Desktop Docs](modules/power-bi-desktop/README.md) | n/a |
| <a name="module_sns-slack-notifications"></a> [aws](#module\_sns-slack-notifications) | [SNS Slack Notifications Docs](modules/sns-slack-notifications/README.md) | n/a |
<!--- END_TF_DOCS --->

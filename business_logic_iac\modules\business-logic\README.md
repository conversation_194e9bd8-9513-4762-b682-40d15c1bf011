# Comtech SmartAnalytics: Business Logic Module

This module will create the resources required to deploy the Comtech Analytics V1 business logic application with CloudWatch monitoring and alerting.

## Usage

```
module "business_logic" {
  source                        = "../modules/business_logic"
  country                       = "us"
  environment                   = "dev"
  ec2_key_pair_name             = "comtech-key-pair"
  mariadb_instance_size         = "db.t3.medium"
  mariadb_max_allocated_storage = 16384
  mariadb_replica_cidr_blocks   = ["0.0.0.0/0"]
  private_subnets_ids           = module.vpc.private_subnets_ids
  collectorapi_ami              = "ami-0c55b159cbfafe1f0"
  vpc_id                        = module.vpc.vpc_id
  vpc_cidr_block                = module.vpc.vpc_cidr_block
  api_acm_arn                   = module.dns.api_acm_arn
  api_subdomain_name            = module.dns.api_subdomain_name
  domain_hosted_zone_id         = module.dns.domain_hosted_zone_id

  # Optional: SNS topic ARN for CloudWatch alarms
  alarm_sns_topic_arn           = module.sns_slack_notifications.sns_topic_arn
}
```

**NOTE**: Refer to the [documentation](https://docs.aws.amazon.com/AmazonRDS/latest/UserGuide/CHAP_Storage.html#CHAP_Storage.Other.Factors) to determine the appropriate value for mariadb_max_allocated_storage depending on mariadb_instance_size.

## CloudWatch Alarms

This module includes CloudWatch monitoring with the following alarms:

### EC2 Monitoring
- **CPU Utilization**: Triggers when CPU > 80% for 2 of 3 evaluation periods (15 minutes)
- **System Status Check**: Auto-recovery enabled for system failures

### RDS Monitoring (Master & Replica)
- **CPU Utilization**: Triggers when CPU > 80% for 2 of 3 evaluation periods
- **Freeable Memory**: Triggers when memory < 512MB for 2 of 3 evaluation periods
- **Free Storage Space**: Triggers when storage < 10GB for 2 of 3 evaluation periods
- **Replication Lag**: Triggers when lag > 300 seconds for 2 of 3 evaluation periods

### ALB Monitoring
- **Unhealthy Host Count**: Triggers when any unhealthy targets detected
- **Response Time**: Triggers when response time > 20 seconds for 2 of 3 evaluation periods
- **4XX Error Rate**: Triggers when error rate > 5% for 2 of 3 evaluation periods
- **5XX Error Rate**: Triggers when error rate > 1% for 2 of 3 evaluation periods

### Alarm Configuration
- **Evaluation Period**: 5 minutes for responsive monitoring
- **Smart Logic**: 2 of 3 datapoints breaching reduces false positives
- **Auto-Recovery**: EC2 instances automatically recover from system failures
- **Notifications**: All alarms send notifications to configured SNS topic

To enable alarms, provide the `alarm_sns_topic_arn` variable from the `sns-slack-notifications` module.


<!--- BEGIN_TF_DOCS --->
## Requirements

| Name | Version |
|------|---------|
| <a name="requirement_terraform"></a> [terraform](#requirement\_terraform) | >= 1.6.3 |
| <a name="requirement_aws"></a> [aws](#requirement\_aws) | >= 5.24.0 |

## Providers

| Name | Version |
|------|---------|
| <a name="provider_aws"></a> [aws](#provider\_aws) | >= 5.24.0 |

## Resources

| Name | Type |
|------|------|
| [aws_iam_instance_profile](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/iam_instance_profile) | resource |
| [aws_iam_policy](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/iam_policy) | resource |
| [aws_iam_policy_document](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/iam_policy_document) | data source |
| [aws_iam_role](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/iam_role) | resource |
| [aws_iam_role_policy_attachment](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/iam_role_policy_attachment) | resource |
| [aws_instance](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/instance) | resource |
| [aws_ecr_repository](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/ecr_repository) | resource |
| [aws_lb](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/lb) | resource |
| [aws_lb_listener](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/lb_listener) | resource |
| [aws_lb_target_group](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/lb_target_group) | resource |
| [aws_db_subnet_group](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/db_subnet_group) | resource |
| [aws_db_instance](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/db_instance) | resource |
| [aws_lb_target_group_attachment](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/lb_target_group_attachment) | resource |
| [aws_route53_record](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/route53_record) | resource |
| [aws_security_group](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/security_group) | resource |
| [aws_cloudwatch_metric_alarm](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/cloudwatch_metric_alarm) | resource |
| [aws_secretsmanager_secret](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/data-sources/secretsmanager_secret) | data source |
| [aws_secretsmanager_secret_version](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/data-sources/secretsmanager_secret_version) | data source |
| [aws_caller_identity.current](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/data-sources/caller_identity) | data source |
| [aws_region.current](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/data-sources/region) | data source |


## Inputs

|Inputs| Name | Description | Type | Default | Required |
|------|------|-------------|------|---------|----------|
| country | n/a | County | `string` |  | yes |
| ec2_key_pair_name | n/a | EC2 Key Pair Name | `string`  |  | yes |
| environment | n/a | Environment | `string`  |  | yes |
| mariadb_instance_size | n/a | EC2 instance size for MariaDB | `string`  | "db.t3.medium" | yes |
| mariadb_max_allocated_storage | n/a | Maximum allocated storage for MariaDB instance | `number`  |  | no |
| mariadb_replica_cidr_blocks | n/a | MariaDB replica ingress cidr blocks | `list(string)`  |  | yes |
| private_subnets_ids | n/a | A list of private subnets | `list(string)` |  | yes |
| collectorapi_ami | n/a | Region specific collectorAPI AMI | `string`  |  | yes |
| vpc_id | n/a | VPC ID | `string`  |  | yes |
| vpc_cidr_block | n/a | VPC CIDR block | `string`  |  | yes |
| api_acm_arn | n/a | API Certificate ARN | `string`  |  | yes |
| api_subdomain_name | n/a | Domain name for the certificate | `string`  |  | yes |
| domain_hosted_zone_id | n/a | Domain hosted zone id | `string`  |  | yes |
| alarm_sns_topic_arn | n/a | SNS topic ARN for CloudWatch alarms | `string`  |  | no |

## Outputs

| Outputs| Name | Description |
|--------|------|-------------|
| bastion_server_ip | n/a | Bastion server public IP |
| collectorapi_ip | n/a | Bastion server private IP |
| mariadb_master_endpoint | n/a | MariaDB master endpiont |
| mariadbreplica_endpoint | n/a | MariaDB replica endpiont |
<!--- END_TF_DOCS --->

inputs = {
  aws_region = "us-east-1"
  environment = "dev"
  country = "us"
  customer_config = {
    "washingtoncounty-mn" = {
      name = "washingtoncounty-mn"
      enabled = true
      tags = {
        approval_date = "08/15/2025"
        billing       = "cust_washingtoncounty_mn_dev"
        ChargeCode    = "03SMAL.GENN.0000.WA9.DEV"
        customer      = "washingtoncounty_mn"
      }
    }
    "flagler" = {
      name = "flagler"
      enabled = true
      tags = {
        approval_date = "08/15/2025"
        billing       = "cust_flagler_fl_dev"
        ChargeCode    = "03SMAL.GENN.0000.WA9.DEV"
        customer      = "flagler_fl"
      }
    }
    "pvgt" = {
      name = "pvgt"
      enabled = true
      tags = {
        approval_date = "08/15/2025"
        billing       = "cust_pvgt_dev"
        ChargeCode    = "03SMAL.GENN.0000.WA9.DEV"
        customer      = "pvgt"
      }
    }
  }
  
  tags = merge(
    include.root.locals.tags,
    {
      country     = "us"
      environment = "dev"
      Module      = "kms"
    }
  )
}

terraform {
  source = "../../../../modules/kms/"
}

include "root" {
  expose = true
  path   = find_in_parent_folders()
}

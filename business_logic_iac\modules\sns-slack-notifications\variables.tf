variable "aws_region" {
  description = "AWS Region"
  type        = string
}

variable "environment" {
  description = "Environment (dev, qa, prod)"
  type        = string
  validation {
    condition     = contains(["dev", "qa", "prod"], var.environment)
    error_message = "Valid value is one of the following: dev, qa, or prod."
  }
}

variable "country" {
  description = "Country code (us, ca)"
  type        = string
  validation {
    condition     = contains(["ca", "us"], var.country)
    error_message = "Valid value is one of the following: ca OR us."
  }
}

variable "tags" {
  type        = map(string)
  description = "Map of key/value pairs to apply as tags to all resources"
  default     = {}
}

variable "slack_webhook_url_vault_path" {
  description = "Vault path to the Slack webhook URL secret"
  type        = string
}

# Email addresses to subscribe to the SNS topic
variable "email_subscriptions" {
  description = "List of email addresses to subscribe to the SNS topic"
  type        = list(string)
  default     = []
}

# SNS Slack Notifications Module

This Terraform module creates a shared SNS topic with Slack integration for CloudWatch alarm notifications. It's designed to be reusable across multiple modules.

## Features

- **Shared SNS Topic**: Single topic for all CloudWatch alarms in an environment
- **Slack Integration**: Automatic notifications via webhook stored in Vault
- **Security**: Webhook URL stored securely in HashiCorp Vault
- **Reusable**: Can be referenced by multiple modules for alarm notifications
- **Cost Effective**: Single Lambda function and SNS topic per environment

## Architecture

```mermaid
graph LR
    A[CloudWatch Alarms] -->|alarm_actions| B(SNS Topic)
    B -->|Subscriptions| C[Lambda Function]
    C -->|POST| E[Slack Webhook]
```

## Usage

### Basic Usage

```hcl
module "sns_slack_notifications" {
  source = "../../../../modules/sns-slack-notifications/"

  aws_region  = "us-east-1"
  environment = "prod"
  country     = "us"

  # Vault path for Slack webhook URL
  slack_webhook_url_vault_path = "secret/prod-nsoc/slack_webhooks/smartanalytics-alerts-prod"

  tags = {
    Environment = "prod"
    Project     = "smartanalytics"
  }
}
```

### Using in Other Modules

```hcl
# In your module that creates alarms
resource "aws_cloudwatch_metric_alarm" "example" {
  alarm_name          = "example-alarm"
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = "2"
  metric_name         = "CPUUtilization"
  namespace           = "AWS/EC2"
  period              = "300"
  statistic           = "Average"
  threshold           = "80"
  alarm_description   = "Example alarm"
  
  # Reference the shared SNS topic
  alarm_actions = [var.alarm_sns_topic_arn]
  ok_actions    = [var.alarm_sns_topic_arn]

  dimensions = {
    InstanceId = aws_instance.example.id
  }
}
```

## Prerequisites

### Vault Secret Setup

Create the Slack webhook URL secret in HashiCorp Vault:

## Variables

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|----------|
| aws_region | AWS Region | string | n/a | yes |
| environment | Environment (dev, qa, prod) | string | n/a | yes |
| country | Country code (us, ca) | string | n/a | yes |
| slack_webhook_url_vault_path | Vault path to the Slack webhook URL secret | string | n/a | yes |
| tags | Map of tags to apply to resources | map(string) | {} | no |

## Outputs

| Name | Description |
|------|-------------|
| sns_topic_arn | SNS topic ARN for CloudWatch alarms |
| sns_topic_name | SNS topic name for CloudWatch alarms |
| sns_topic_id | SNS topic ID for CloudWatch alarms |

## Vault Secret Paths

The module expects the Slack webhook URL to be stored in Vault at:
```
secret/{vault_namespace}/slack_webhooks/smartanalytics-alerts-{environment}
```

Examples:
- `secret/dev-nsoc/slack_webhooks/smartanalytics-alerts-dev`
- `secret/dev-nsoc/slack_webhooks/smartanalytics-alerts-qa`
- `secret/prod-nsoc/slack_webhooks/smartanalytics-alerts-prod`

**Note**: US and CA share the same environment-based secrets (dev, qa, prod).

## Security Considerations

1. **Secure Storage**: Webhook URLs are stored securely in HashiCorp Vault
2. **Vault Authentication**: Module uses Vault provider authentication
3. **Encryption**: All data is encrypted at rest and in transit
4. **Access Control**: Only authorized services can access the webhook URL

## Dependencies

- AWS Provider >= 5.24.0
- Vault Provider >= 3.0.0
- Terraform >= 1.6.3
- External cloudwatch_alarm_slack_action module
- HashiCorp Vault with appropriate secrets

## Deployment Order

1. **Create Vault secrets** for Slack webhook URLs
2. **Deploy SNS module** first
3. **Deploy modules with alarms** that reference the SNS topic


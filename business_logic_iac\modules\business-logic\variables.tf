variable "aws_region" {
  description = "AWS Region"
  type        = string
}


variable "country" {
  description = "Country"
  type        = string
}


variable "ec2_key_pair_name" {
  description = "EC2 Key Pair Name"
  type        = string
}


variable "environment" {
  description = "Environment"
  type        = string
}


variable "mariadb_instance_size" {
  description = "EC2 instance size for MariaDB"
  type        = string
  default     = "db.t4g.large"
}


variable "mariadb_min_allocated_storage" {
  description = "Minimum allocated storage for MariaDB instance"
  type        = number
  default     = 100
}


variable "mariadb_max_allocated_storage" {
  description = "Maximum allocated storage for MariaDB instance"
  type        = number
  default     = 0
}


variable "mariadb_replica_cidr_blocks" {
  description = "MariaDB replica ingress cidr blocks"
  type        = list(string)
}


variable "private_subnets_ids" {
  description = "A list of private subnets"
  type        = list(string)
}


variable "tags" {
  type        = map(string)
  description = "Map of key/value pairs to apply as tags to all resources. Defaults to {}."
  default     = {}
}


variable "collectorapi_ami" {
  description = "Region specific collectorAPI AMI"
  type        = string
}

variable "collectorapi_instance_size" {
  description = "EC2 instance size for CollectorAPI"
  type        = string
  default     = "t3a.small"
}

variable "vpc_id" {
  description = "VPC ID"
  type        = string
}

variable "vpc_cidr_block" {
  description = "VPC CIDR block"
  type        = string
}

variable "api_acm_arn" {
  description = "API Certificate ARN"
  type        = string
}

variable "api_subdomain_name" {
  description = "Domain name for the certificate"
  type        = string
}

variable "domain_hosted_zone_id" {
  description = "Domain hosted zone id"
  type        = string
}

variable "alarm_sns_topic_arn" {
  description = "SNS topic ARN for CloudWatch alarms"
  type        = string
  default     = ""
}
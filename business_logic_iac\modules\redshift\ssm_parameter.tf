resource "aws_ssm_parameter" "redshift_credentials" {
  depends_on = [
    random_password.redshift_master_password
  ]

  name        = "/${var.environment}/${var.country}/smartanalytics/${var.customer}_redshift_credentials"
  description = "${var.customer} redshift credentials"
  type        = "SecureString"
  value = jsonencode({
    "master_username" : var.redshift_master_username,
    "master_password" : random_password.redshift_master_password.result,
    "endpoint" : aws_route53_record.redshift_cname_record.fqdn
  })

  tags = {
    Name = "${local.prefix}-redshift-credentials"
  }
}

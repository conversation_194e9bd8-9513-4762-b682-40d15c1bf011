# Comtech SmartAnalytics: Power BI Desktop Module

This module will create the resources required to deploy an AWS Redshift provisioned data warehouse

## Usage

```
module "redshift_data_warehouse" {
  source = "../../../../modules/redshift"

  aws_region               = "aws_region"
  aws_vpc_id               = "aws_vpc_id"
  customer                 = "customer"
  country                  = "country"
  domain_hosted_zone_id    = "domain_hosted_zone_id"
  environment              = "environment"
  private_subnets_ids      = ["private_subnets_ids"]
  redshift_cluster_type    = "redshift_cluster_type"
  redshift_master_username = "redshift_master_username"
  redshift_port            = 5439
  redshift_node_type       = "ra3.large"
  redshift_number_of_nodes = 1
  tags                     = {tags = "true"}
  vpc_cidr_block           = "vpc_cidr_block"
}
```


<!--- BEGIN_TF_DOCS --->
## Requirements

| Name | Version |
|------|---------|
| <a name="requirement_terraform"></a> [terraform](#requirement\_terraform) | >= 1.6 |
| <a name="requirement_aws"></a> [aws](#requirement\_aws) | >= 5.24 |

## Providers

| Name | Version |
|------|---------|
| <a name="provider_aws"></a> [aws](#provider\_aws) | >= 5.24 |

## Resources

| Name | Type |
|------|------|
| [aws_iam_role_policy]() | resource |
| [aws_iam_role]() | resource |
| [aws_redshift_cluster]() | resource |
| [aws_redshift_subnet_group]() | resource |
| [aws_route53_record]() | resource |
| [aws_security_group](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/security_group) | resource |

## Inputs

|Inputs| Name | Description | Type | Default | Required |
|------|------|-------------|------|---------|----------|
| aws_region | n/a | AWS region | `string`  |  | yes |
| aws_vpc_id | n/a | AWS VPC ID | `string`  |  | yes |
| customer | n/a | Customer | `string` |  | yes |
| country | n/a | County | `string` |  | yes |
| domain_hosted_zone_id | n/a | Domain hosted zone id | `string` |  | yes |
| environment | n/a | Environment | `string`  |  | yes |
| private_subnets_ids | n/a | A list of public subnets | `list(string)` |  |yes |
| redshift_cluster_type | n/a | The cluster type to use. Either single-node or multi-node | `string`  |  | yes |
| redshift_master_username | n/a | Master username | `string`  | solacom | yes |
| redshift_port | n/a | The Port the cluster responds on | `string`  | 5439 | no |
| redshift_node_type | n/a | The node type to be provisioned for the cluster | `string`  |  | yes |
| redshift_number_of_nodes | n/a | The number of compute nodes in the cluster. This parameter is required when the ClusterType parameter is specified as multi-node | `string`  |  | no |
| tags | n/a | Map of key/value pairs to apply as tags to all resources | `string`  |  | yes |
| vpc_cidr_block | n/a | VPC CIDR block | `string`  |  | yes |


## Outputs

| Outputs| Name | Description |
|--------|------|-------------|
| redshift_cname | redshift_cluster_cname | Redshift CNAME |
| redshift_dns_name | redshift_cluster_dns_name | Redshift DNS Name |
<!--- END_TF_DOCS --->

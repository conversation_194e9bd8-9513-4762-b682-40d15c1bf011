# EC2 — CPU (5m avg > 80% for 15m)
resource "aws_cloudwatch_metric_alarm" "ec2_cpu_utilization" {
  count                     = local.create_alarms ? 1 : 0
  alarm_name                = "${local.name_prefix}-ec2-cpu-utilization"
  comparison_operator       = "GreaterThanThreshold"
  evaluation_periods        = 3 # 3 * 5m = 15m
  datapoints_to_alarm       = 2 # 2 of 3 breaching
  metric_name               = "CPUUtilization"
  namespace                 = "AWS/EC2"
  period                    = 300 # 5 minutes
  statistic                 = "Average"
  threshold                 = 80
  alarm_description         = "EC2 CPU > 80% (5m) for 15m"
  alarm_actions             = local.alarm_actions
  treat_missing_data        = "missing"
  insufficient_data_actions = []

  dimensions = {
    InstanceId = aws_instance.collectorapi.id
  }

  tags = merge(var.tags, {
    Name      = "${local.name_prefix}-ec2-cpu-utilization"
    AlarmType = "EC2Warning"
  })
}

# EC2 — System status check (auto-recover)
resource "aws_cloudwatch_metric_alarm" "ec2_status_check_failed_system" {
  count               = local.create_alarms ? 1 : 0
  alarm_name          = "${local.name_prefix}-ec2-status-check-failed-system"
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = 2
  datapoints_to_alarm = 1
  metric_name         = "StatusCheckFailed_System"
  namespace           = "AWS/EC2"
  period              = 300 # 1 minute
  statistic           = "Maximum"
  threshold           = 0
  alarm_description   = "EC2 system status check failed"
  alarm_actions = [
    "arn:aws:automate:${data.aws_region.current.name}:ec2:recover"
  ]
  # What to do when alarm transitions from ALARM to OK
  ok_actions                = local.alarm_actions
  treat_missing_data        = "notBreaching"
  insufficient_data_actions = []

  dimensions = {
    InstanceId = aws_instance.collectorapi.id
  }

  tags = merge(var.tags, {
    Name      = "${local.name_prefix}-ec2-status-check-failed-system"
    AlarmType = "EC2Critical"
  })
}

# RDS (Master) — CPU, FreeableMemory, FreeStorage, Connections
resource "aws_cloudwatch_metric_alarm" "rds_master_cpu_utilization" {
  count                     = local.create_alarms ? 1 : 0
  alarm_name                = "${local.name_prefix}-rds-master-cpu-utilization"
  comparison_operator       = "GreaterThanThreshold"
  evaluation_periods        = 3
  datapoints_to_alarm       = 2
  metric_name               = "CPUUtilization"
  namespace                 = "AWS/RDS"
  period                    = 300 # 5 minutes
  statistic                 = "Average"
  threshold                 = 80
  alarm_description         = "RDS master CPU > 80% (5m) for 15m"
  alarm_actions             = local.alarm_actions
  treat_missing_data        = "missing"
  insufficient_data_actions = []

  dimensions = {
    DBInstanceIdentifier = aws_db_instance.mariadb_master_internal.identifier
  }

  tags = merge(var.tags, {
    Name      = "${local.name_prefix}-rds-master-cpu-utilization"
    AlarmType = "RDSWarning"
  })
}

resource "aws_cloudwatch_metric_alarm" "rds_master_freeable_memory" {
  count                     = local.create_alarms ? 1 : 0
  alarm_name                = "${local.name_prefix}-rds-master-freeable-memory"
  comparison_operator       = "LessThanThreshold"
  evaluation_periods        = 3
  datapoints_to_alarm       = 2
  metric_name               = "FreeableMemory"
  namespace                 = "AWS/RDS"
  period                    = 300 # 5 minutes
  statistic                 = "Average"
  threshold                 = 536870912 # 512 MB
  alarm_description         = "RDS master FreeableMemory < 512MB (5m) for 15m"
  alarm_actions             = local.alarm_actions
  treat_missing_data        = "missing"
  insufficient_data_actions = []

  dimensions = {
    DBInstanceIdentifier = aws_db_instance.mariadb_master_internal.identifier
  }

  tags = merge(var.tags, {
    Name      = "${local.name_prefix}-rds-master-freeable-memory"
    AlarmType = "RDSWarning"
  })
}

resource "aws_cloudwatch_metric_alarm" "rds_master_free_storage_space" {
  count                     = local.create_alarms ? 1 : 0
  alarm_name                = "${local.name_prefix}-rds-master-free-storage-space"
  comparison_operator       = "LessThanThreshold"
  evaluation_periods        = 3
  datapoints_to_alarm       = 2
  metric_name               = "FreeStorageSpace"
  namespace                 = "AWS/RDS"
  period                    = 300 # 5 minutes
  statistic                 = "Average"
  threshold                 = 10737418240 # 10 GB
  alarm_description         = "RDS master FreeStorageSpace < 10GB (5m) for 15m"
  alarm_actions             = local.alarm_actions
  treat_missing_data        = "missing"
  insufficient_data_actions = []

  dimensions = {
    DBInstanceIdentifier = aws_db_instance.mariadb_master_internal.identifier
  }

  tags = merge(var.tags, {
    Name      = "${local.name_prefix}-rds-master-free-storage-space"
    AlarmType = "RDSCritical"
  })
}

# RDS (Replica) — CPU, Memory, Storage, ReplicaLag
resource "aws_cloudwatch_metric_alarm" "rds_replica_lag" {
  count                     = local.create_alarms ? 1 : 0
  alarm_name                = "${local.name_prefix}-rds-replica-lag"
  comparison_operator       = "GreaterThanThreshold"
  evaluation_periods        = 3
  datapoints_to_alarm       = 2
  metric_name               = "ReplicaLag"
  namespace                 = "AWS/RDS"
  period                    = 300 # 5 minutes
  statistic                 = "Average"
  threshold                 = 300 # 5 minutes lag
  alarm_description         = "RDS replica lag > 300s (5m) for 15m"
  alarm_actions             = local.alarm_actions
  treat_missing_data        = "missing"
  insufficient_data_actions = []

  dimensions = {
    DBInstanceIdentifier = aws_db_instance.mariadb_replica_internal.identifier
  }

  tags = merge(var.tags, {
    Name      = "${local.name_prefix}-rds-replica-lag"
    AlarmType = "RDSCritical"
  })
}

resource "aws_cloudwatch_metric_alarm" "rds_replica_cpu_utilization" {
  count                     = local.create_alarms ? 1 : 0
  alarm_name                = "${local.name_prefix}-rds-replica-cpu-utilization"
  comparison_operator       = "GreaterThanThreshold"
  evaluation_periods        = 3
  datapoints_to_alarm       = 2
  metric_name               = "CPUUtilization"
  namespace                 = "AWS/RDS"
  period                    = 300 # 5 minutes
  statistic                 = "Average"
  threshold                 = 80
  alarm_description         = "RDS replica CPU > 80% (5m) for 15m"
  alarm_actions             = local.alarm_actions
  treat_missing_data        = "missing"
  insufficient_data_actions = []

  dimensions = {
    DBInstanceIdentifier = aws_db_instance.mariadb_replica_internal.identifier
  }

  tags = merge(var.tags, {
    Name      = "${local.name_prefix}-rds-replica-cpu-utilization"
    AlarmType = "RDSWarning"
  })
}

resource "aws_cloudwatch_metric_alarm" "rds_replica_freeable_memory" {
  count                     = local.create_alarms ? 1 : 0
  alarm_name                = "${local.name_prefix}-rds-replica-freeable-memory"
  comparison_operator       = "LessThanThreshold"
  evaluation_periods        = 3
  datapoints_to_alarm       = 2
  metric_name               = "FreeableMemory"
  namespace                 = "AWS/RDS"
  period                    = 300 # 5 minutes
  statistic                 = "Average"
  threshold                 = 536870912 # 512 MB 
  alarm_description         = "RDS replica FreeableMemory < 512MB (5m) for 15m"
  alarm_actions             = local.alarm_actions
  treat_missing_data        = "missing"
  insufficient_data_actions = []

  dimensions = {
    DBInstanceIdentifier = aws_db_instance.mariadb_replica_internal.identifier
  }

  tags = merge(var.tags, {
    Name      = "${local.name_prefix}-rds-replica-freeable-memory"
    AlarmType = "RDSWarning"
  })
}

resource "aws_cloudwatch_metric_alarm" "rds_replica_free_storage_space" {
  count                     = local.create_alarms ? 1 : 0
  alarm_name                = "${local.name_prefix}-rds-replica-free-storage-space"
  comparison_operator       = "LessThanThreshold"
  evaluation_periods        = 3
  datapoints_to_alarm       = 2
  metric_name               = "FreeStorageSpace"
  namespace                 = "AWS/RDS"
  period                    = 300
  statistic                 = "Average"
  threshold                 = 10737418240 # 10 GB
  alarm_description         = "RDS replica FreeStorageSpace < 10GB (5m) for 15m"
  alarm_actions             = local.alarm_actions
  treat_missing_data        = "missing"
  insufficient_data_actions = []

  dimensions = {
    DBInstanceIdentifier = aws_db_instance.mariadb_replica_internal.identifier
  }

  tags = merge(var.tags, {
    Name      = "${local.name_prefix}-rds-replica-free-storage-space"
    AlarmType = "RDSCritical"
  })
}

# ALB — UNHEALTHY HOST COUNT
resource "aws_cloudwatch_metric_alarm" "alb_unhealthy_host_count" {
  count                     = local.create_alarms ? 1 : 0
  alarm_name                = "${local.name_prefix}-alb-unhealthy-host-count"
  comparison_operator       = "GreaterThanThreshold"
  evaluation_periods        = 3
  datapoints_to_alarm       = 1
  metric_name               = "UnHealthyHostCount"
  namespace                 = "AWS/ApplicationELB"
  period                    = 300 # 5 minutes
  statistic                 = "Maximum"
  threshold                 = 0
  alarm_description         = "Unhealthy targets detected in target group"
  alarm_actions             = local.alarm_actions
  treat_missing_data        = "notBreaching"
  insufficient_data_actions = []

  dimensions = {
    LoadBalancer = aws_lb.collectorapi_alb.arn_suffix
    TargetGroup  = aws_lb_target_group.collectorapi_tg.arn_suffix
  }

  tags = merge(var.tags, {
    Name      = "${local.name_prefix}-alb-unhealthy-host-count"
    AlarmType = "ALBCritical"
  })
}

# ALB — RESPONSE TIME (TargetResponseTime)
resource "aws_cloudwatch_metric_alarm" "alb_response_time" {
  count                     = local.create_alarms ? 1 : 0
  alarm_name                = "${local.name_prefix}-alb-response-time"
  comparison_operator       = "GreaterThanThreshold"
  evaluation_periods        = 3
  datapoints_to_alarm       = 2
  metric_name               = "TargetResponseTime"
  namespace                 = "AWS/ApplicationELB"
  period                    = 300
  statistic                 = "Average"
  threshold                 = 20 # 20 seconds
  alarm_description         = "ALB TargetResponseTime > 20s (5m) for 15m"
  alarm_actions             = local.alarm_actions
  treat_missing_data        = "notBreaching"
  insufficient_data_actions = []

  dimensions = {
    LoadBalancer = aws_lb.collectorapi_alb.arn_suffix
    TargetGroup  = aws_lb_target_group.collectorapi_tg.arn_suffix
  }

  tags = merge(var.tags, {
    Name      = "${local.name_prefix}-alb-response-time"
    AlarmType = "ALBWarning"
  })
}

# ALB — 4XX ERROR RATE
# rate = HTTPCode_Target_4XX_Count / RequestCount * 100
resource "aws_cloudwatch_metric_alarm" "alb_4xx_error_rate" {
  count                     = local.create_alarms ? 1 : 0
  alarm_name                = "${local.name_prefix}-alb-4xx-error-rate"
  comparison_operator       = "GreaterThanThreshold"
  evaluation_periods        = 3
  datapoints_to_alarm       = 2
  threshold                 = 5 # 5% warning
  alarm_description         = "ALB target 4XX error rate > 5% (5m) for 15m"
  alarm_actions             = local.alarm_actions
  treat_missing_data        = "notBreaching"
  insufficient_data_actions = []

  # Expression (must return data)
  metric_query {
    id          = "rate"
    expression  = "IF(req>0, (err4xx/req)*100, 0)"
    label       = "Target 4XX Error Rate (%)"
    return_data = true
  }

  # 4XX count
  metric_query {
    id = "err4xx"
    metric {
      metric_name = "HTTPCode_Target_4XX_Count"
      namespace   = "AWS/ApplicationELB"
      period      = 300
      stat        = "Sum"
      dimensions = {
        LoadBalancer = aws_lb.collectorapi_alb.arn_suffix
        TargetGroup  = aws_lb_target_group.collectorapi_tg.arn_suffix
      }
    }
    return_data = false
  }

  # Requests
  metric_query {
    id = "req"
    metric {
      metric_name = "RequestCount"
      namespace   = "AWS/ApplicationELB"
      period      = 300
      stat        = "Sum"
      dimensions = {
        LoadBalancer = aws_lb.collectorapi_alb.arn_suffix
        TargetGroup  = aws_lb_target_group.collectorapi_tg.arn_suffix
      }
    }
    return_data = false
  }

  tags = merge(var.tags, {
    Name      = "${local.name_prefix}-alb-4xx-error-rate"
    AlarmType = "ALBWarning"
  })
}

# ALB — 5XX ERROR RATE
# rate = HTTPCode_Target_5XX_Count / RequestCount * 100
resource "aws_cloudwatch_metric_alarm" "alb_5xx_error_rate" {
  count                     = local.create_alarms ? 1 : 0
  alarm_name                = "${local.name_prefix}-alb-5xx-error-rate"
  comparison_operator       = "GreaterThanThreshold"
  evaluation_periods        = 3
  datapoints_to_alarm       = 2
  threshold                 = 1 # 1% critical
  alarm_description         = "ALB target 5XX error rate > 1% (5m) for 15m"
  alarm_actions             = local.alarm_actions
  treat_missing_data        = "notBreaching"
  insufficient_data_actions = []

  # Expression (must return data)
  metric_query {
    id          = "rate"
    expression  = "IF(req>0, (err5xx/req)*100, 0)"
    label       = "Target 5XX Error Rate (%)"
    return_data = true
  }

  # 5XX count
  metric_query {
    id = "err5xx"
    metric {
      metric_name = "HTTPCode_Target_5XX_Count"
      namespace   = "AWS/ApplicationELB"
      period      = 300
      stat        = "Sum"
      dimensions = {
        LoadBalancer = aws_lb.collectorapi_alb.arn_suffix
        TargetGroup  = aws_lb_target_group.collectorapi_tg.arn_suffix
      }
    }
    return_data = false
  }

  # Requests
  metric_query {
    id = "req"
    metric {
      metric_name = "RequestCount"
      namespace   = "AWS/ApplicationELB"
      period      = 300
      stat        = "Sum"
      dimensions = {
        LoadBalancer = aws_lb.collectorapi_alb.arn_suffix
        TargetGroup  = aws_lb_target_group.collectorapi_tg.arn_suffix
      }
    }
    return_data = false
  }

  tags = merge(var.tags, {
    Name      = "${local.name_prefix}-alb-5xx-error-rate"
    AlarmType = "ALBCritical"
  })
}

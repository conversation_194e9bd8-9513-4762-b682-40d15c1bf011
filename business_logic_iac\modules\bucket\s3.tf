# S3 Bucket per customer
resource "aws_s3_bucket" "bucket" {
  for_each = local.enabled_customers

  bucket = "${local.name_prefix}-${each.value.customer_name}-${var.bucket_name}"
  lifecycle {
    prevent_destroy = true
  }

  tags = merge(
    var.tags,
    each.value.tags,
    {
      Name = "${local.name_prefix}-${each.value.customer_name}-${var.bucket_name}"
    }
  )
}

# S3 Bucket Versioning
resource "aws_s3_bucket_versioning" "bucket" {
  for_each = local.enabled_customers

  bucket = aws_s3_bucket.bucket[each.key].id
  versioning_configuration {
    status = "Enabled"
  }
}

# S3 Bucket Server-side Encryption
resource "aws_s3_bucket_server_side_encryption_configuration" "bucket" {
  for_each = local.enabled_customers

  bucket = aws_s3_bucket.bucket[each.key].id
  rule {
    apply_server_side_encryption_by_default {
      kms_master_key_id = each.value.kms_key_arn
      sse_algorithm     = "aws:kms"
    }
    bucket_key_enabled = true
  }
}

# S3 Bucket Public Access Block
resource "aws_s3_bucket_public_access_block" "bucket" {
  for_each = local.enabled_customers

  bucket                  = aws_s3_bucket.bucket[each.key].id
  block_public_acls       = true
  block_public_policy     = true
  ignore_public_acls      = true
  restrict_public_buckets = true
}

# S3 Bucket Lifecycle Configuration
resource "aws_s3_bucket_lifecycle_configuration" "bucket" {
  for_each = local.enabled_customers

  bucket = aws_s3_bucket.bucket[each.key].id
  rule {
    id     = "expire_noncurrent_versions"
    status = "Enabled"
    filter {}

    noncurrent_version_expiration {
      noncurrent_days = var.s3_lifecycle_noncurrent_version_expiration_days
    }
  }

  depends_on = [aws_s3_bucket_versioning.bucket]
}

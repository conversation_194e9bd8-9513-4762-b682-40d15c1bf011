dependency "kms" {
  config_path = "../kms"
  mock_outputs_allowed_terraform_commands = ["validate", "plan", "show"]
  mock_outputs = {
    kms_key_arns = {}
    kms_key_ids = {}
    kms_key_aliases = {}
  }
}

inputs = {
  aws_region = "ca-central-1"
  environment = "prod"
  country = "ca"
  bucket_name = "lambdas"
  # TODO: Provide PROD customer objects
  customer_config = {}
  
  s3_lifecycle_noncurrent_version_expiration_days = 30
  
  tags = merge(
    include.root.locals.tags,
    {
      country     = "ca"
      environment = "prod"
      Module      = "s3"
    }
  )
}

terraform {
  source = "../../../../modules/bucket/"
}

include "root" {
  expose = true
  path   = find_in_parent_folders()
}

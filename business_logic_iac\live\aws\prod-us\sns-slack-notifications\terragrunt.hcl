inputs = {
  aws_region  = "us-east-1"
  environment = "prod"
  country     = "us"

  # Vault path for Slack webhook URL
  slack_webhook_url_vault_path = "secret/prod-nsoc/slack_webhooks/smartanalytics-alerts-prod"

  tags = merge(
    include.root.locals.tags,
    {
      ChargeCode  = "03SMAL.GENN.0000.WA9.DEV"
      country     = "us"
      environment = "prod"
      Module      = "sns-slack-notifications"
    }
  )
}

terraform {
  source = "git::ssh://****************************/cloud-infrastructure/comtechanalytics-infrastructure.git//business_logic_iac/modules/sns-slack-notifications?ref=main"
}

include "root" {
  expose = true
  path   = find_in_parent_folders()
}

inputs = {
  aws_region  = "us-east-1"
  environment = "qa"
  country     = "us"

  # Vault path for Slack webhook URL
  slack_webhook_url_vault_path = "secret/dev-nsoc/slack_webhooks/smartanalytics-alerts-qa"

  tags = merge(
    include.root.locals.tags,
    {
      ChargeCode  = "03SMAL.GENN.0000.WA9.DEV"
      country     = "us"
      environment = "qa"
      Module      = "sns-slack-notifications"
    }
  )
}

terraform {
  source = "../../../../modules/sns-slack-notifications/"
}

include "root" {
  expose = true
  path   = find_in_parent_folders()
}

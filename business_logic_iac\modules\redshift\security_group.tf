resource "aws_security_group" "redshift_security_group" {
  description = "Redshift security group"
  name        = "${local.prefix}-security-group"
  vpc_id      = var.aws_vpc_id

  ingress {
    description = "Allow all inbound traffic from VPC CIDR block"
    cidr_blocks = [
      var.vpc_cidr_block,
    ]
    from_port = var.redshift_port
    to_port   = var.redshift_port
    protocol  = "tcp"
  }

  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }

  tags = {
    Name = "${local.prefix}-security-group"
  }
}

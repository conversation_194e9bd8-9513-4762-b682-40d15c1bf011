# Generic S3 Bucket Module

This Terraform module creates customer-specific S3 buckets for various use cases. Each customer gets their own dedicated bucket with configurable naming and encryption.

## Features

- **Customer-specific S3 buckets** with configurable naming
- **KMS encryption** using customer-managed keys
- **Versioning enabled** for object history
- **Lifecycle policies** to manage storage costs
- **Public access blocked** for security
- **Bucket key enabled** for cost optimization
- **Flexible configuration** for different use cases (Lambda code, data storage, etc.)

## Usage

### Lambda Deployment Buckets
```hcl
module "lambda_buckets" {
  source = "../../../../modules/bucket/"

  aws_region = "us-east-1"
  environment = "dev"
  country = "us"
  bucket_name = "lambdas"

  customer_config = {
    "washingtoncounty-mn" = {
      enabled = true
      kms_key_arn = "arn:aws:kms:us-east-1:123456789012:key/washingtoncounty-mn-key-id"
      customer_name = "washingtoncounty-mn"
    }
    "flagler" = {
      enabled = true
      kms_key_arn = "arn:aws:kms:us-east-1:123456789012:key/flagler-key-id"
      customer_name = "flagler"
    }
  }

  tags = {
    Environment = "dev"
    Project     = "smartanalytics"
  }
}
```

### Data Storage Buckets
```hcl
module "data_buckets" {
  source = "../../../../modules/bucket/"

  aws_region = "us-east-1"
  environment = "dev"
  country = "us"
  bucket_name = "data-storage"

  customer_config = {
    "customer1" = {
      enabled = true
      kms_key_arn = "arn:aws:kms:us-east-1:123456789012:key/customer1-key-id"
      customer_name = "customer1"
    }
  }
}
```

## Bucket Structure

Each customer gets their own S3 bucket with configurable naming:
```
{environment}-{country}-smartanalytics-{customer}-{bucket_name}/

Examples:
dev-us-smartanalytics-flagler-lambdas/
├── acd-processor.zip
├── data-transformer.zip
└── ...

prod-us-smartanalytics-flagler-data-storage/
├── raw-data/
├── processed-data/
└── ...
```

## Security

- **Public access blocked**: All public access is denied
- **KMS encryption**: All objects encrypted with customer-managed key
- **Bucket key enabled**: Reduces KMS costs for high-volume operations
- **Versioning**: Maintains history of deployment packages

## Lifecycle Management

- **Non-current versions**: Automatically deleted after 30 days (configurable)
- **Current versions**: Retained indefinitely
- **Incomplete multipart uploads**: Cleaned up automatically

## Variables

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| aws_region | AWS Region | `string` | n/a | yes |
| environment | Environment (dev, qa, prod) | `string` | n/a | yes |
| country | Country code (us, ca) | `string` | n/a | yes |
| customer_config | Customer configuration map | `map(object({enabled=bool, kms_key_arn=string, customer_name=string}))` | n/a | yes |
| bucket_name | Name suffix for the S3 bucket | `string` | n/a | yes |
| s3_lifecycle_noncurrent_version_expiration_days | Days to retain non-current versions | `number` | `30` | no |
| tags | Resource tags | `map(string)` | `{}` | no |

## Outputs

| Name | Description |
|------|-------------|
| s3_bucket_names | Map of customer names to S3 bucket names |
| s3_bucket_arns | Map of customer names to S3 bucket ARNs |
| s3_bucket_ids | Map of customer names to S3 bucket IDs |

## Dependencies

- Requires KMS keys from `kms` module
- Must be deployed before modules that require S3 buckets

## Deployment Order

1. Deploy `kms` module
2. Deploy `bucket` module (depends on KMS)
3. Deploy modules that require S3 buckets (depends on bucket module)

## Best Practices

1. **Use consistent naming**: Follow the established naming convention
2. **Tag appropriately**: Include environment, project, and cost center tags
3. **Regular cleanup**: Review and clean up unused deployment packages

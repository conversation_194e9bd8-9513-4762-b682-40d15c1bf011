inputs = {
  aws_region = "ca-central-1"
  environment = "prod"
  country = "ca"
  # TODO: Provide PROD customer objects
  customer_config = {}
  
  tags = merge(
    include.root.locals.tags,
    {
      country     = "ca"
      environment = "prod"
      Module      = "kms"
    }
  )
}

terraform {
  source = "../../../../modules/kms/"
}

include "root" {
  expose = true
  path   = find_in_parent_folders()
}

inputs = {
  aws_region = "us-east-1"
  environment = "prod"
  country = "us"
  # TODO: Provide PROD customer objects
  customer_config = {}
  
  tags = merge(
    include.root.locals.tags,
    {
      country     = "us"
      environment = "prod"
      Module      = "kms"
    }
  )
}

terraform {
  source = "../../../../modules/kms/"
}

include "root" {
  expose = true
  path   = find_in_parent_folders()
}

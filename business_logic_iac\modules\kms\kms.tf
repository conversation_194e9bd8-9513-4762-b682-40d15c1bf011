# KMS Key for customer-specific smart analytics resources
resource "aws_kms_key" "smart_analytics_key" {
  for_each = local.enabled_customers

  description             = "KMS key for ${each.key} smart analytics resources"
  deletion_window_in_days = 7
  enable_key_rotation     = true

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Sid    = "Enable IAM Root User Permissions"
        Effect = "Allow"
        Principal = {
          AWS = "arn:aws:iam::${data.aws_caller_identity.current.account_id}:root"
        }
        Action   = ["kms:*"]
        Resource = ["*"]
      },
      {
        Sid    = "Allow Lambda service to use the key"
        Effect = "Allow"
        Principal = {
          Service = "lambda.amazonaws.com"
        }
        Action = [
          "kms:Decrypt",
          "kms:DescribeKey",
          "kms:GenerateDataKey*",
          "kms:CreateGrant",
          "kms:ListGrants"
        ]
        Resource = ["*"]
        Condition = {
          StringEquals = {
            "kms:ViaService" = "lambda.${data.aws_region.current.name}.amazonaws.com"
          }
          Bool = { "kms:GrantIsForAWSResource" = "true" }
          # Restrict to customer-specific Lambda functions
          StringLike = {
            "kms:EncryptionContext:aws:lambda:FunctionName" = [
              "arn:aws:lambda:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:function:${local.name_prefix}-${each.key}-*"
            ]
          }
        }
      },
      {
        Sid    = "Allow S3 service to use the key"
        Effect = "Allow"
        Principal = {
          Service = "s3.amazonaws.com"
        }
        Action = [
          "kms:Decrypt",
          "kms:Encrypt",
          "kms:ReEncrypt*",
          "kms:GenerateDataKey*",
          "kms:DescribeKey",
          "kms:CreateGrant",
          "kms:ListGrants"
        ]
        Resource = ["*"]
        Condition = {
          StringEquals = {
            "kms:ViaService" = "s3.${data.aws_region.current.name}.amazonaws.com"
          }
          Bool = { "kms:GrantIsForAWSResource" = "true" }
          # Restrict to customer-specific S3 buckets
          StringLike = {
            "kms:EncryptionContext:aws:s3:arn" = [
              "arn:aws:s3:::${local.name_prefix}-${each.key}-*",
              "arn:aws:s3:::${local.name_prefix}-${each.key}-*/*"
            ]
          }
        }
      }
    ]
  })

  tags = merge(
    var.tags,
    each.value.tags,
    {
      Name = "${local.name_prefix}-${each.key}-key"
    }
  )
}

# KMS Key Alias
resource "aws_kms_alias" "smart_analytics_key_alias" {
  for_each = local.enabled_customers

  name          = "alias/${local.name_prefix}-${each.key}-key"
  target_key_id = aws_kms_key.smart_analytics_key[each.key].key_id
}

variable "aws_region" {
  description = "AWS Region"
  type        = string
}


variable "aws_vpc_id" {
  description = "VPC ID to deploy resources"
  type        = string
}


variable "customer" {
  description = "Customer"
  type        = string
}


variable "country" {
  description = "Country"
  type        = string
}


variable "domain_hosted_zone_id" {
  description = "Domain hosted zone id"
  type        = string
}


variable "environment" {
  description = "Environment"
  type        = string
}


variable "private_subnets_ids" {
  description = "A list of private subnets"
  type        = list(string)
}


variable "redshift_cluster_type" {
  description = "The cluster type to use. Either single-node or multi-node"
  type        = string
  default     = "multi-node"
}


variable "redshift_master_username" {
  description = "Master username"
  type        = string
  default     = "solacom"
}


variable "redshift_port" {
  description = "The Port the cluster responds on"
  type        = number
  default     = 5439
}


variable "redshift_node_type" {
  description = "The node type to be provisioned for the cluster"
  type        = string
  default     = "ra3.large"
}


variable "redshift_number_of_nodes" {
  description = "The number of compute nodes in the cluster. This parameter is required when the ClusterType parameter is specified as multi-node"
  type        = number
  default     = 1
}


variable "tags" {
  type        = map(string)
  description = "Map of key/value pairs to apply as tags to all resources. Defaults to {}."
  default     = {}
}


variable "vpc_cidr_block" {
  description = "VPC CIDR block"
  type        = string
}

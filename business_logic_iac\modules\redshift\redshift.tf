resource "random_password" "redshift_master_password" {
  length      = 16
  min_lower   = 2
  min_numeric = 2
  min_special = 2
  min_upper   = 2
  special     = true
}


resource "aws_redshift_cluster" "redshift_cluster" {
  depends_on = [
    aws_redshift_subnet_group.redshift_subnet_group,
    random_password.redshift_master_password
  ]

  cluster_identifier = "${local.prefix}-redshift"
  #TODO cluster_parameter_group_name = ""
  cluster_subnet_group_name = aws_redshift_subnet_group.redshift_subnet_group.id
  cluster_type              = var.redshift_cluster_type
  #TODO database_name       = "" 
  encrypted              = true
  iam_roles              = [aws_iam_role.redshift_role.arn]
  publicly_accessible    = false
  master_username        = var.redshift_master_username
  master_password        = random_password.redshift_master_password.result
  node_type              = var.redshift_node_type
  number_of_nodes        = var.redshift_number_of_nodes
  skip_final_snapshot    = true
  vpc_security_group_ids = [aws_security_group.redshift_security_group.id]

  tags = {
    Name = "${local.prefix}-redshift"
  }
}
